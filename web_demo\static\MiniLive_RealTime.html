<!doctype html>
<html lang="en-us">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0"/>
        <link rel="icon" href="common/favicon.ico" type="image/x-icon">
        <title>MiniLive - 实时数字人</title>
        <style>
            :root {
                --primary-color: #4361ee;
                --secondary-color: #3f37c9;
                --accent-color: #f72585;
                --light-color: #f8f9fa;
                --dark-color: #212529;
                --success-color: #4cc9f0;
                --border-radius: 12px;
                --box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }
            
            body {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
                background: linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%);
                font-family: 'Arial', sans-serif;
                overflow: hidden;
                transition: background 0.5s ease;
            }

            video, canvas {
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
            }

            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            #canvas_gl {
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 128px;
                height: 128px;
            }

            #screen {
                position: absolute;
                bottom: -1000;
                right: -1000;
                width: 1px;
                height: 1px;
            }

            #screen2 {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: none;
                z-index: 5;
            }

            #startMessage {
                position: absolute;
                top: 60%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 24px;
                font-weight: bold;
                color: var(--dark-color);
                z-index: 10;
                background-color: rgba(255, 255, 255, 0.85);
                padding: 15px 30px;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                transition: all 0.3s ease;
                backdrop-filter: blur(5px);
            }

            /* 控制面板容器 */
            #controlPanel {
                position: absolute;
                top: 20px;
                left: 20px;
                z-index: 10;
                background-color: rgba(255, 255, 255, 0.85);
                padding: 15px;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                transition: all 0.3s ease;
                display: flex;
                flex-direction: column;
                gap: 15px;
                backdrop-filter: blur(5px);
                border: 1px solid rgba(255, 255, 255, 0.5);
                max-height: 80vh;
                overflow-y: auto;
            }

            .control-group {
                display: flex;
                flex-direction: column;
            }

            .custom-select {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                padding: 12px 40px 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: var(--dark-color);
                background-color: #fff;
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                cursor: pointer;
                outline: none;
                transition: all 0.3s ease;
                width: 180px;
            }

            .custom-select:hover {
                border-color: var(--secondary-color);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
                transform: translateY(-2px);
            }

            .custom-select:focus {
                border-color: var(--accent-color);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
            }

            .select-label {
                display: block;
                margin-bottom: 8px;
                font-weight: bold;
                color: var(--dark-color);
            }

            .control-group::after {
                content: '▼';
                position: absolute;
                right: 25px;
                transform: translateY(32px);
                pointer-events: none;
                color: var(--primary-color);
                font-size: 12px;
            }

            /* 背景色配置样式 */
            .background-config {
                border-top: 1px solid rgba(0, 0, 0, 0.1);
                padding-top: 15px;
                margin-top: 10px;
            }

            .color-input-group {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
            }

            #colorPicker {
                width: 50px;
                height: 40px;
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                cursor: pointer;
                outline: none;
                transition: all 0.3s ease;
            }

            #colorPicker:hover {
                border-color: var(--secondary-color);
                transform: translateY(-2px);
            }

            #hexInput {
                flex: 1;
                padding: 10px 15px;
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                font-size: 14px;
                outline: none;
                transition: all 0.3s ease;
            }

            #hexInput:focus {
                border-color: var(--accent-color);
                box-shadow: 0 0 8px rgba(247, 37, 133, 0.4);
            }

            .preset-colors {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 8px;
                margin-bottom: 10px;
            }

            .preset-color-btn {
                width: 35px;
                height: 35px;
                border: 2px solid #fff;
                border-radius: var(--border-radius);
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .preset-color-btn:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            }

            .preset-color-btn.active {
                border-color: var(--accent-color);
                border-width: 3px;
            }

            .control-buttons {
                display: flex;
                gap: 8px;
            }

            .bg-control-btn {
                flex: 1;
                padding: 8px 12px;
                border: none;
                border-radius: var(--border-radius);
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .reset-btn {
                background-color: var(--primary-color);
                color: white;
            }

            .reset-btn:hover {
                background-color: var(--secondary-color);
                transform: translateY(-2px);
            }

            .random-btn {
                background-color: var(--success-color);
                color: white;
            }

            .random-btn:hover {
                background-color: #3ba3cc;
                transform: translateY(-2px);
            }

            #loadingSpinner {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 20;
                background-color: rgba(255, 255, 255, 0.9);
                padding: 25px;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                text-align: center;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }

            #loadingSpinner strong {
                display: block;
                margin-bottom: 15px;
                color: var(--primary-color);
                font-size: 20px;
            }

            .spinner {
                border: 4px solid rgba(0, 0, 0, 0.1);
                border-radius: 50%;
                border-top: 4px solid var(--primary-color);
                width: 50px;
                height: 50px;
                margin: 0 auto;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            #canvasEl {
                position: absolute;
                left: -9999px;
                top: -9999px;
                width: 300px;
                height: 150px;
            }

            /* 弹幕容器 */
            #danmakuContainer {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                overflow: hidden;
                z-index: 8;
            }

            /* 弹幕样式 */
            .danmaku {
                position: absolute;
                white-space: nowrap;
                font-weight: bold;
                color: white;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                animation-name: danmaku-move;
                animation-timing-function: linear;
                animation-fill-mode: forwards;
                user-select: none;
            }

            @keyframes danmaku-move {
                from { transform: translateX(100%); }
                to { transform: translateX(-100%); }
            }

            /* 弹幕发送区域 - 移到右上角 */
            #danmakuInputContainer {
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 15;
                display: flex;
                align-items: center;
                background-color: rgba(255, 255, 255, 0.85);
                padding: 10px;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                transition: all 0.3s ease;
                backdrop-filter: blur(5px);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }

            #danmakuInput {
                width: 180px;
                padding: 10px 15px;
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                font-size: 16px;
                outline: none;
                transition: all 0.3s ease;
            }

            #danmakuInput:focus {
                border-color: var(--accent-color);
                box-shadow: 0 0 8px rgba(247, 37, 133, 0.4);
            }

            #sendDanmakuBtn {
                margin-left: 10px;
                padding: 10px 15px;
                background-color: var(--primary-color);
                color: white;
                border: none;
                border-radius: var(--border-radius);
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            #sendDanmakuBtn:hover {
                background-color: var(--secondary-color);
                transform: translateY(-2px);
            }

            @media (max-width: 768px) {
                #controlPanel {
                    padding: 10px;
                    max-width: calc(100vw - 40px);
                }

                .custom-select {
                    padding: 10px 30px 10px 15px;
                    font-size: 14px;
                    width: 150px;
                }

                #danmakuInputContainer {
                    top: 20px;
                    right: 20px;
                    flex-direction: column;
                    width: auto;
                }

                #danmakuInput {
                    width: 120px;
                    margin-bottom: 10px;
                }

                #sendDanmakuBtn {
                    width: 100%;
                    margin-left: 0;
                    padding: 8px 10px;
                }

                /* 背景色配置移动端优化 */
                .background-config {
                    padding-top: 10px;
                    margin-top: 8px;
                }

                .color-input-group {
                    gap: 8px;
                    margin-bottom: 8px;
                }

                #colorPicker {
                    width: 40px;
                    height: 35px;
                }

                #hexInput {
                    font-size: 14px;
                    padding: 8px 12px;
                }

                .preset-colors {
                    grid-template-columns: repeat(3, 1fr);
                    gap: 6px;
                    margin-bottom: 8px;
                }

                .preset-color-btn {
                    width: 30px;
                    height: 30px;
                }

                .control-buttons {
                    gap: 6px;
                }

                .bg-control-btn {
                    padding: 6px 10px;
                    font-size: 12px;
                }

                .select-label {
                    font-size: 14px;
                    margin-bottom: 6px;
                }
            }
        </style>
    </head>
    <body>
        <!-- 控制面板 - 将角色和声音选择放在同一侧 -->
        <div id="controlPanel">
            <div class="control-group">
                <label for="characterDropdown" class="select-label">选择角色</label>
                <select id="characterDropdown" class="custom-select">
                    <option value="assets">女性1</option>
                    <option value="assets12">女性2</option>
                    <option value="assets13">女性3</option>
                    <option value="assets14">女性4</option>
                    <option value="assets4">女性5</option>
                    <option value="assets8">女性6</option>
                    <option value="assets2">女性7</option>
                    <option value="assets7">女性8</option>
                    <!-- <option value="assets7">女性四</option> -->
                    <option value="assets5">男性一</option>
                </select>
            </div>
            <div class="control-group">
                <label for="voiceDropdown" class="select-label">选择声音</label>
                <select id="voiceDropdown" class="custom-select">
                    <option value=0>温柔女</option>
                    <option value=1>温柔男</option>
                    <option value=2>甜美女</option>
                    <option value=3>青年女</option>
                    <option value=4>磁性男</option>
                </select>
            </div>

            <!-- 背景色配置区域 -->
            <div class="background-config">
                <label class="select-label">背景色配置</label>

                <!-- 颜色选择器和HEX输入 -->
                <div class="color-input-group">
                    <input type="color" id="colorPicker" value="#8EC5FC" title="选择背景色">
                    <input type="text" id="hexInput" placeholder="#8EC5FC" maxlength="7" title="输入HEX颜色值">
                </div>

                <!-- 预设颜色按钮 -->
                <div class="preset-colors">
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%)"
                         style="background: linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%);"
                         title="默认渐变"></div>
                    <div class="preset-color-btn" data-bg="#FF6B6B"
                         style="background: #FF6B6B;"
                         title="珊瑚红"></div>
                    <div class="preset-color-btn" data-bg="#4ECDC4"
                         style="background: #4ECDC4;"
                         title="青绿色"></div>
                    <div class="preset-color-btn" data-bg="#45B7D1"
                         style="background: #45B7D1;"
                         title="天蓝色"></div>
                    <div class="preset-color-btn" data-bg="#96CEB4"
                         style="background: #96CEB4;"
                         title="薄荷绿"></div>
                    <div class="preset-color-btn" data-bg="#FFEAA7"
                         style="background: #FFEAA7;"
                         title="柠檬黄"></div>
                    <div class="preset-color-btn" data-bg="#DDA0DD"
                         style="background: #DDA0DD;"
                         title="紫罗兰"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(45deg, #FF9A9E 0%, #FECFEF 50%, #FECFEF 100%)"
                         style="background: linear-gradient(45deg, #FF9A9E 0%, #FECFEF 50%, #FECFEF 100%);"
                         title="粉色渐变"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                         style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"
                         title="紫蓝渐变"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
                         style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);"
                         title="粉红渐变"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
                         style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);"
                         title="蓝色渐变"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)"
                         style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);"
                         title="绿色渐变"></div>
                </div>

                <!-- 控制按钮 -->
                <div class="control-buttons">
                    <button class="bg-control-btn reset-btn" id="resetBgBtn" title="重置为默认背景">重置</button>
                    <button class="bg-control-btn random-btn" id="randomBgBtn" title="随机背景色">随机</button>
                </div>
            </div>
        </div>

        <figure id="loadingSpinner">
            <strong>MiniMates 加载中...</strong>
            <div class="spinner"></div>
        </figure>
        <canvas id="canvasEl"></canvas>
        <canvas id="canvas_video"></canvas>
        <canvas id="canvas_gl" width="128" height="128"></canvas>
        <div id="screen"></div>
        <iframe id="screen2" src="dialog_RealTime.html" frameborder="0" style="display: none;"></iframe>
        <div id="startMessage">加载中</div>

        <!-- 弹幕容器 -->
        <div id="danmakuContainer"></div>

        <!-- 弹幕输入区域 - 移到右上角 -->
        <div id="danmakuInputContainer">
            <input type="text" id="danmakuInput" placeholder="发送弹幕..." maxlength="50">
            <button id="sendDanmakuBtn">发送</button>
        </div>

        <!-- 文字朗读功能 - 添加在底部 -->
        <div id="ttsInputContainer" style="
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 15;
            display: flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.85);
            padding: 10px 15px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            max-width: 80%;
            width: auto;
        ">
            <input type="text" id="ttsInput" placeholder="输入文字让数字人朗读..." style="
                flex: 1;
                min-width: 250px;
                padding: 12px 15px;
                border: 2px solid #4361ee;
                border-radius: 12px;
                font-size: 16px;
                outline: none;
                transition: all 0.3s ease;
            ">
            <button id="readTextBtn" style="
                margin-left: 10px;
                padding: 12px 20px;
                background-color: #4361ee;
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            ">朗读</button>
        </div>

        <script src="js/pako.min.js"></script>
        <script src="js/mp4box.all.min.js"></script>
        <script src="js/DHLiveMini.js"></script>
        <script src="js/MiniMateLoader.js"></script>
        <script src="js/MiniLive2.js"></script>
        <script src="js/simple_digital_human.js"></script>

        <!-- 背景色配置功能脚本 -->
        <script>
            // 背景色配置功能实现
            document.addEventListener('DOMContentLoaded', function() {
                const colorPicker = document.getElementById('colorPicker');
                const hexInput = document.getElementById('hexInput');
                const presetColorBtns = document.querySelectorAll('.preset-color-btn');
                const resetBgBtn = document.getElementById('resetBgBtn');
                const randomBgBtn = document.getElementById('randomBgBtn');
                const body = document.body;

                // 默认背景
                const defaultBackground = 'linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%)';

                // 随机颜色数组
                const randomColors = [
                    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
                    '#FF8A80', '#80CBC4', '#81C784', '#FFB74D', '#F06292', '#BA68C8',
                    '#64B5F6', '#4DB6AC', '#AED581', '#FFD54F', '#FF8A65', '#A1887F'
                ];

                // 随机渐变色数组
                const randomGradients = [
                    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                    'linear-gradient(45deg, #FF9A9E 0%, #FECFEF 50%, #FECFEF 100%)',
                    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                    'linear-gradient(135deg, #ff8a00 0%, #e52e71 100%)',
                    'linear-gradient(135deg, #667db6 0%, #0082c8 0%, #0082c8 0%, #667db6 100%)',
                    'linear-gradient(135deg, #f12711 0%, #f5af19 100%)'
                ];

                // 工具函数：将HEX转换为RGB
                function hexToRgb(hex) {
                    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                    return result ? {
                        r: parseInt(result[1], 16),
                        g: parseInt(result[2], 16),
                        b: parseInt(result[3], 16)
                    } : null;
                }

                // 工具函数：将RGB转换为HEX
                function rgbToHex(r, g, b) {
                    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                }

                // 工具函数：验证HEX颜色格式
                function isValidHex(hex) {
                    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
                }

                // 防抖函数
                function debounce(func, wait) {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                }

                // 设置背景色
                function setBackground(background) {
                    body.style.background = background;

                    // 如果是纯色，更新颜色选择器和HEX输入
                    if (background.startsWith('#')) {
                        colorPicker.value = background;
                        hexInput.value = background.toUpperCase();
                    } else if (background.startsWith('rgb')) {
                        // 处理RGB格式
                        const rgbMatch = background.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
                        if (rgbMatch) {
                            const hex = rgbToHex(parseInt(rgbMatch[1]), parseInt(rgbMatch[2]), parseInt(rgbMatch[3]));
                            colorPicker.value = hex;
                            hexInput.value = hex.toUpperCase();
                        }
                    } else {
                        // 渐变色情况，清空输入框
                        hexInput.value = '';
                        hexInput.placeholder = '渐变色';
                    }

                    // 更新预设按钮的激活状态
                    updatePresetButtonsState(background);
                }

                // 更新预设按钮的激活状态
                function updatePresetButtonsState(currentBackground) {
                    presetColorBtns.forEach(btn => {
                        btn.classList.remove('active');
                        if (btn.dataset.bg === currentBackground) {
                            btn.classList.add('active');
                        }
                    });
                }

                // 颜色选择器变化事件
                colorPicker.addEventListener('input', function() {
                    const color = this.value;
                    setBackground(color);
                });

                // HEX输入框变化事件（使用防抖优化性能）
                const debouncedHexInput = debounce(function(value, inputElement) {
                    if (isValidHex(value)) {
                        setBackground(value);
                        inputElement.style.borderColor = 'var(--primary-color)';
                    }
                }, 300);

                hexInput.addEventListener('input', function() {
                    let value = this.value.trim();

                    // 自动添加#号
                    if (value && !value.startsWith('#')) {
                        value = '#' + value;
                        this.value = value;
                    }

                    // 实时验证颜色格式
                    if (isValidHex(value)) {
                        this.style.borderColor = 'var(--primary-color)';
                        debouncedHexInput(value, this);
                    } else if (value.length > 1) {
                        this.style.borderColor = '#ff4757';
                    } else {
                        this.style.borderColor = 'var(--primary-color)';
                    }
                });

                // HEX输入框失去焦点时的处理
                hexInput.addEventListener('blur', function() {
                    this.style.borderColor = 'var(--primary-color)';
                    if (this.value && !isValidHex(this.value)) {
                        this.value = colorPicker.value.toUpperCase();
                    }
                });

                // 预设颜色按钮点击事件
                presetColorBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const background = this.dataset.bg;
                        setBackground(background);
                    });
                });

                // 重置按钮点击事件
                resetBgBtn.addEventListener('click', function() {
                    setBackground(defaultBackground);
                });

                // 随机背景按钮点击事件
                randomBgBtn.addEventListener('click', function() {
                    const useGradient = Math.random() > 0.5;
                    let randomBackground;

                    if (useGradient) {
                        randomBackground = randomGradients[Math.floor(Math.random() * randomGradients.length)];
                    } else {
                        randomBackground = randomColors[Math.floor(Math.random() * randomColors.length)];
                    }

                    setBackground(randomBackground);
                });

                // 初始化：设置默认激活状态
                updatePresetButtonsState(defaultBackground);

                console.log("背景色配置功能初始化完成");
            });
        </script>

        <!-- 弹幕功能脚本 -->
        <script>
            // 弹幕功能实现
            document.addEventListener('DOMContentLoaded', function() {
                const danmakuContainer = document.getElementById('danmakuContainer');
                const danmakuInput = document.getElementById('danmakuInput');
                const sendDanmakuBtn = document.getElementById('sendDanmakuBtn');
                
                // 随机颜色数组
                const colors = [
                    '#FFFFFF', // 白色
                    '#FF8A80', // 红色
                    '#FFFF8D', // 黄色
                    '#CCFF90', // 绿色
                    '#80D8FF', // 蓝色
                    '#B388FF', // 紫色
                    '#FFD180', // 橙色
                    '#F48FB1'  // 粉色
                ];
                
                // 发送弹幕函数
                function sendDanmaku(text) {
                    if (!text.trim()) return;
                    
                    // 创建弹幕元素
                    const danmaku = document.createElement('div');
                    danmaku.className = 'danmaku';
                    danmaku.textContent = text;
                    
                    // 随机设置弹幕样式
                    const fontSize = Math.floor(Math.random() * 16) + 20; // 20-36px
                    const color = colors[Math.floor(Math.random() * colors.length)];
                    const top = Math.floor(Math.random() * 80) + 5; // 5-85%
                    const duration = Math.floor(Math.random() * 5) + 8; // 8-13秒
                    
                    danmaku.style.fontSize = `${fontSize}px`;
                    danmaku.style.color = color;
                    danmaku.style.top = `${top}%`;
                    danmaku.style.animationDuration = `${duration}s`;
                    
                    // 添加到容器
                    danmakuContainer.appendChild(danmaku);
                    
                    // 动画结束后移除
                    setTimeout(() => {
                        danmaku.remove();
                    }, duration * 1000);
                }
                
                // 点击发送按钮
                sendDanmakuBtn.addEventListener('click', function() {
                    const text = danmakuInput.value;
                    sendDanmaku(text);
                    danmakuInput.value = '';
                });
                
                // 按回车发送
                danmakuInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const text = danmakuInput.value;
                        sendDanmaku(text);
                        danmakuInput.value = '';
                    }
                });
                
                // 监听对话框中的消息，自动生成弹幕
                window.addEventListener('message', function(event) {
                    if (event.data && event.data.type === 'aiMessage') {
                        sendDanmaku(event.data.text);
                    }
                });
                
                // 初始隐藏弹幕输入框，等加载完成后显示
                const danmakuInputContainer = document.getElementById('danmakuInputContainer');
                danmakuInputContainer.style.display = 'none';
                
                // 监听加载完成事件
                const loadingCheck = setInterval(() => {
                    if (document.getElementById('loadingSpinner').style.display === 'none') {
                        danmakuInputContainer.style.display = 'flex';
                        clearInterval(loadingCheck);
                    }
                }, 1000);
            });
        </script>

        <!-- 文字朗读功能脚本 -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const ttsInput = document.getElementById('ttsInput');
                const readTextBtn = document.getElementById('readTextBtn');
                const ttsInputContainer = document.getElementById('ttsInputContainer');
                
                // 初始隐藏TTS输入框，等加载完成后显示
                ttsInputContainer.style.display = 'none';
                
                // 监听加载完成事件
                const loadingCheck = setInterval(() => {
                    if (document.getElementById('loadingSpinner').style.display === 'none') {
                        ttsInputContainer.style.display = 'flex';
                        clearInterval(loadingCheck);
                    }
                }, 1000);
                
                // 朗读文字函数
                async function readText(text) {
                    if (!text.trim()) return;
                    
                    try {
                        // 显示加载状态
                        readTextBtn.textContent = '处理中...';
                        readTextBtn.disabled = true;
                        
                        // 获取当前选择的声音ID
                        const voiceDropdown = document.getElementById('voiceDropdown');
                        const voiceId = voiceDropdown ? voiceDropdown.value : "0";
                        
                        console.log("准备发送TTS请求:", text, voiceId);
                        
                        // 准备请求数据
                        const requestData = {
                            input_mode: "text",
                            prompt: text,
                            voice_id: voiceId,
                            voice_speed: "",
                            tts_only: true  // 添加标记表示只需要TTS，不需要对话
                        };
                        
                        // 发送请求到服务器
                        const response = await fetch('/eb_stream', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(requestData)
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP错误 ${response.status}`);
                        }
                        
                        console.log("收到服务器响应");
                        
                        // 处理流式响应
                        const reader = response.body.getReader();
                        const decoder = new TextDecoder();
                        let buffer = '';
                        
                        while (true) {
                            const { done, value } = await reader.read();
                            
                            if (done) {
                                console.log("响应流结束");
                                break;
                            }
                            
                            // 将新数据添加到缓冲区
                            const chunk = decoder.decode(value, { stream: true });
                            console.log("收到数据块:", chunk.length, "字节");
                            buffer += chunk;
                            
                            // 根据换行符拆分缓冲区中的数据
                            const lines = buffer.split('\n');
                            buffer = lines.pop(); // 保留最后一个可能不完整的块
                            
                            for (const line of lines) {
                                if (line.trim()) {
                                    try {
                                        console.log("解析JSON:", line);
                                        const data = JSON.parse(line);
                                        
                                        if (data.audio) {
                                            console.log("收到音频数据，长度:", data.audio.length);
                                            // 播放音频
                                            playAudio(data.audio, text);
                                        } else {
                                            console.log("JSON中没有音频数据");
                                        }
                                    } catch (e) {
                                        console.error('解析JSON时出错:', e, line);
                                    }
                                }
                            }
                        }
                        
                        // 处理缓冲区中剩余的数据
                        if (buffer.trim()) {
                            try {
                                console.log("处理剩余数据:", buffer);
                                const data = JSON.parse(buffer);
                                if (data.audio) {
                                    console.log("收到最后的音频数据");
                                    playAudio(data.audio, text);
                                }
                            } catch (e) {
                                console.error('解析最后一块JSON时出错:', e);
                            }
                        }
                        
                    } catch (error) {
                        console.error('处理文字朗读时出错:', error);
                        alert('朗读失败，请重试: ' + error.message);
                    } finally {
                        // 恢复按钮状态
                        readTextBtn.textContent = '朗读';
                        readTextBtn.disabled = false;
                    }
                }
                
                // 简化的音频播放函数
                function playAudio(audioBase64, text) {
                    if (!audioBase64) {
                        console.error('没有收到音频数据');
                        return;
                    }
                    
                    console.log("创建音频元素");
                    // 创建音频元素
                    const audio = new Audio(`data:audio/wav;base64,${audioBase64}`);
                    
                    // 音频加载错误处理
                    audio.onerror = function(e) {
                        console.error('音频加载错误:', e);
                    };
                    
                    // 音频加载完成后播放
                    audio.oncanplaythrough = function() {
                        console.log("音频加载完成，开始播放");
                        audio.play().catch(e => console.error('播放音频失败:', e));
                    };
                    
                    // 尝试直接播放
                    audio.play().catch(e => {
                        console.log('直接播放失败，等待加载:', e);
                    });
                }
                
                // 点击朗读按钮
                readTextBtn.addEventListener('click', function() {
                    const text = ttsInput.value;
                    readText(text);
                });
                
                // 按回车朗读
                ttsInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const text = ttsInput.value;
                        readText(text);
                    }
                });
                
                console.log("TTS功能初始化完成");
            });
        </script>

        <!-- 口型动画控制脚本 -->
        <script>
            // 口型动画控制
            window.startMouthAnimation = function(duration) {
                console.log("主页面收到口型动画请求，持续时间:", duration);
                
                // 简单的口型动画序列
                const mouthSequence = [
                    [0.1, 0.1, 0.1, 0.1, 0.1, 0.1], // 闭合
                    [0.5, 0.3, 0.2, 0.1, 0.1, 0.1], // 半开
                    [0.8, 0.2, 0.1, 0.1, 0.1, 0.1], // 大开
                    [0.5, 0.3, 0.2, 0.1, 0.1, 0.1], // 半开
                    [0.2, 0.7, 0.3, 0.1, 0.1, 0.1], // 小开
                    [0.1, 0.1, 0.1, 0.1, 0.1, 0.1]  // 闭合
                ];
                
                // 动画帧率（每秒帧数）
                const fps = 10;
                // 计算总帧数
                const totalFrames = Math.floor(duration * fps);
                // 当前帧
                let currentFrame = 0;
                
                // 动画间隔ID
                let animationInterval = setInterval(() => {
                    // 计算当前应该显示的口型索引
                    const mouthIndex = currentFrame % mouthSequence.length;
                    
                    // 应用口型
                    if (window.Module && Module._setLipShape) {
                        try {
                            // 尝试直接设置口型
                            const lipShape = mouthSequence[mouthIndex];
                            Module._setLipShape(
                                lipShape[0], lipShape[1], lipShape[2], 
                                lipShape[3], lipShape[4], lipShape[5]
                            );
                        } catch (e) {
                            console.error("设置口型失败:", e);
                        }
                    }
                    
                    // 增加帧计数
                    currentFrame++;
                    
                    // 如果动画结束，清除间隔
                    if (currentFrame >= totalFrames) {
                        clearInterval(animationInterval);
                        
                        // 重置口型
                        if (window.Module && Module._setLipShape) {
                            try {
                                Module._setLipShape(0, 0, 0, 0, 0, 0);
                            } catch (e) {
                                console.error("重置口型失败:", e);
                            }
                        }
                    }
                }, 1000 / fps);
            };
        </script>
    </body>
</html>
